import { createClient } from '@supabase/supabase-js';

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;
if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables');
}

// Regular client for user operations
export const supabase = createClient(supabaseUrl, supabaseAnonKey);

// Types for our database tables
export interface Organization {
  uuid: string;
  name: string;
  slug: string;
  description: string;
  logo_url?: string;
  is_active: boolean;
  created_by?: string;
  created_at: string;
  updated_at: string;
}

export interface User {
  id: string;
  email: string;
  role?: string;
  organization_id?: string;
  fullname?: string;
  created_at: string;
  updated_at: string;
}
